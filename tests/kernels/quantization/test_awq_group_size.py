# SPDX-License-Identifier: Apache-2.0

import pytest
import torch

from vllm.model_executor.layers.quantization.awq import AWQConfig, AWQLinearMethod


class MockLayer:
    """Mock layer for testing AWQ weight creation."""
    
    def __init__(self):
        self.parameters = {}
    
    def register_parameter(self, name: str, param):
        self.parameters[name] = param
        setattr(self, name, param)


@pytest.mark.parametrize("group_size", [-1, 128, 256])
@pytest.mark.parametrize("input_size", [512, 1024, 2048])
@pytest.mark.parametrize("output_size", [1024, 2048, 4096])
def test_awq_create_weights_group_size(group_size, input_size, output_size):
    """Test AWQ create_weights method with different group_size values.
    
    This test specifically verifies that group_size=-1 is handled correctly
    and doesn't cause negative tensor dimensions.
    """
    # Create AWQ config
    awq_config = AWQConfig(
        weight_bits=4,
        group_size=group_size,
        zero_point=True
    )
    
    # Create AWQ linear method
    awq_method = AWQLinearMethod(awq_config)
    
    # Create mock layer
    layer = MockLayer()
    
    # Test parameters
    input_size_per_partition = input_size
    output_partition_sizes = [output_size]
    params_dtype = torch.float16
    
    # This should not raise any errors, especially not negative dimension errors
    awq_method.create_weights(
        layer=layer,
        input_size_per_partition=input_size_per_partition,
        output_partition_sizes=output_partition_sizes,
        input_size=input_size,
        output_size=output_size,
        params_dtype=params_dtype
    )
    
    # Verify that all required parameters were created
    assert hasattr(layer, 'qweight'), "qweight parameter not created"
    assert hasattr(layer, 'qzeros'), "qzeros parameter not created"
    assert hasattr(layer, 'scales'), "scales parameter not created"
    
    # Calculate expected number of groups
    if group_size == -1:
        expected_group_size = input_size
    else:
        expected_group_size = group_size
    
    expected_num_groups = input_size_per_partition // expected_group_size
    
    # Verify tensor shapes are correct
    assert layer.qweight.shape == (input_size_per_partition, output_size // awq_config.pack_factor)
    assert layer.qzeros.shape == (expected_num_groups, output_size // awq_config.pack_factor)
    assert layer.scales.shape == (expected_num_groups, output_size)
    
    # Verify no negative dimensions
    assert all(dim > 0 for dim in layer.qweight.shape), f"qweight has negative dimensions: {layer.qweight.shape}"
    assert all(dim > 0 for dim in layer.qzeros.shape), f"qzeros has negative dimensions: {layer.qzeros.shape}"
    assert all(dim > 0 for dim in layer.scales.shape), f"scales has negative dimensions: {layer.scales.shape}"


def test_awq_group_size_negative_one_specific():
    """Specific test for the reported bug case with group_size=-1."""
    # This reproduces the exact scenario from the GitHub issue
    awq_config = AWQConfig(
        weight_bits=4,
        group_size=-1,  # This was causing the negative dimension error
        zero_point=True
    )
    
    awq_method = AWQLinearMethod(awq_config)
    layer = MockLayer()
    
    # Parameters similar to the error case (Qwen3-0.6B)
    input_size_per_partition = 1024
    output_partition_sizes = [512]
    input_size = 1024
    output_size = 512
    params_dtype = torch.float16
    
    # This should not raise RuntimeError: Trying to create tensor with negative dimension
    awq_method.create_weights(
        layer=layer,
        input_size_per_partition=input_size_per_partition,
        output_partition_sizes=output_partition_sizes,
        input_size=input_size,
        output_size=output_size,
        params_dtype=params_dtype
    )
    
    # With group_size=-1, effective group_size should be input_size
    # So num_groups = input_size_per_partition // input_size = 1024 // 1024 = 1
    expected_num_groups = 1
    
    assert layer.qzeros.shape[0] == expected_num_groups
    assert layer.scales.shape[0] == expected_num_groups
    
    # Verify all dimensions are positive
    assert all(dim > 0 for dim in layer.qweight.shape)
    assert all(dim > 0 for dim in layer.qzeros.shape)
    assert all(dim > 0 for dim in layer.scales.shape)


@pytest.mark.parametrize("group_size", [64, 128, 256, -1])
def test_awq_group_size_alignment_check(group_size):
    """Test that group_size alignment validation works correctly."""
    awq_config = AWQConfig(
        weight_bits=4,
        group_size=group_size,
        zero_point=True
    )
    
    awq_method = AWQLinearMethod(awq_config)
    layer = MockLayer()
    
    # Use input size that's not divisible by group_size (except for -1)
    input_size = 1000  # Not divisible by 64, 128, or 256
    input_size_per_partition = input_size
    output_partition_sizes = [1024]
    output_size = 1024
    params_dtype = torch.float16
    
    if group_size == -1:
        # group_size=-1 should work (becomes input_size=1000)
        awq_method.create_weights(
            layer=layer,
            input_size_per_partition=input_size_per_partition,
            output_partition_sizes=output_partition_sizes,
            input_size=input_size,
            output_size=output_size,
            params_dtype=params_dtype
        )
        # Should succeed
        assert hasattr(layer, 'qweight')
    else:
        # Other group sizes should raise ValueError due to alignment
        with pytest.raises(ValueError, match="input size is not aligned"):
            awq_method.create_weights(
                layer=layer,
                input_size_per_partition=input_size_per_partition,
                output_partition_sizes=output_partition_sizes,
                input_size=input_size,
                output_size=output_size,
                params_dtype=params_dtype
            )
