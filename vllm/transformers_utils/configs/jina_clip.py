# SPDX-License-Identifier: Apache-2.0

import os
from copy import deepcopy
from typing import Any, Dict, List, Optional, Union

import torch
from transformers import PretrainedConfig, logging

logger = logging.get_logger(__name__)


class JinaCLIPTextConfig(PretrainedConfig):
    """Configuration class for Jina CLIP text encoder."""
    
    model_type = 'jina_clip_text'

    def __init__(
        self,
        embed_dim: int = 1024,
        hf_model_name_or_path: str = 'jinaai/jina-embeddings-v3',
        hf_model_config_kwargs: Optional[Dict[str, Any]] = None,
        default_instruction_task: Optional[str] = None,
        default_lora_task: Optional[str] = None,
        pooler_type: Optional[str] = "mean_pooler",
        proj_type: Optional[str] = None,
        proj_bias: bool = False,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.hf_model_name_or_path = hf_model_name_or_path
        self.hf_model_config_kwargs = hf_model_config_kwargs or {}
        self.default_instruction_task = default_instruction_task
        self.default_lora_task = default_lora_task
        self.pooler_type = pooler_type
        self.proj_type = proj_type
        self.proj_bias = proj_bias

    @classmethod
    def from_pretrained(
        cls, pretrained_model_name_or_path: Union[str, os.PathLike], **kwargs
    ) -> 'PretrainedConfig':
        cls._set_token_in_kwargs(kwargs)
        config_dict, kwargs = cls.get_config_dict(
            pretrained_model_name_or_path, **kwargs
        )
        
        # get the text config dict if we are loading from JinaCLIPConfig
        if config_dict.get('model_type') == 'jina_clip':
            config_dict = config_dict['text_config']
            
        if (
            'model_type' in config_dict
            and hasattr(cls, 'model_type')
            and config_dict['model_type'] != cls.model_type
        ):
            logger.warning(
                f'You are using a model of type {config_dict["model_type"]} to '
                f'instantiate a model of type {cls.model_type}. This is not supported '
                'for all configurations of models and can yield errors.'
            )
            
        return cls.from_dict(config_dict, **kwargs)


class JinaCLIPVisionConfig(PretrainedConfig):
    """Configuration class for Jina CLIP vision encoder."""
    
    model_type = 'jina_clip_vision'

    def __init__(
        self,
        embed_dim: int = 1024,
        width: int = 1024,
        image_size: int = 512,
        patch_size: int = 14,
        layers: int = 24,
        head_width: int = 64,
        mlp_ratio: float = 2.6667,
        ls_init_value: Optional[float] = None,
        patch_dropout: float = 0.1,
        qkv_bias: bool = True,
        fused_layer_norm: bool = False,
        x_attention: bool = True,
        post_norm: bool = False,
        rope_embeddings: bool = True,
        pt_hw_seq_len: int = 16,
        intp_freq: bool = True,
        naive_swiglu: bool = True,
        subln: bool = True,
        drop_path_rate: float = 0.0,
        proj_type: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.layers = layers
        self.embed_dim = embed_dim
        self.width = width
        self.head_width = head_width
        self.mlp_ratio = mlp_ratio
        self.image_size = image_size
        self.patch_size = patch_size
        self.ls_init_value = ls_init_value
        self.patch_dropout = patch_dropout
        self.qkv_bias = qkv_bias
        self.fused_layer_norm = fused_layer_norm
        self.x_attention = x_attention
        self.post_norm = post_norm
        self.rope_embeddings = rope_embeddings
        self.pt_hw_seq_len = pt_hw_seq_len
        self.intp_freq = intp_freq
        self.naive_swiglu = naive_swiglu
        self.subln = subln
        self.drop_path_rate = drop_path_rate
        self.proj_type = proj_type

    @classmethod
    def from_pretrained(
        cls, pretrained_model_name_or_path: Union[str, os.PathLike], **kwargs
    ) -> 'PretrainedConfig':
        cls._set_token_in_kwargs(kwargs)
        config_dict, kwargs = cls.get_config_dict(
            pretrained_model_name_or_path, **kwargs
        )
        
        # get the vision config dict if we are loading from JinaCLIPConfig
        if config_dict.get('model_type') == 'jina_clip':
            config_dict = config_dict['vision_config']
            
        if (
            'model_type' in config_dict
            and hasattr(cls, 'model_type')
            and config_dict['model_type'] != cls.model_type
        ):
            logger.warning(
                f'You are using a model of type {config_dict["model_type"]} to '
                f'instantiate a model of type {cls.model_type}. This is not supported '
                'for all configurations of models and can yield errors.'
            )
            
        return cls.from_dict(config_dict, **kwargs)


class JinaCLIPConfig(PretrainedConfig):
    """Configuration class for Jina CLIP model."""
    
    model_type = 'jina_clip'
    is_composition = True

    def __init__(
        self,
        text_config: Optional[Dict] = None,
        vision_config: Optional[Dict] = None,
        add_projections: bool = False,
        projection_dim: int = 1024,
        logit_scale_init_value: float = 2.6592,
        use_text_flash_attn: Optional[bool] = None,
        use_vision_xformers: Optional[bool] = None,
        matryoshka_dimensions: Optional[List[int]] = None,
        truncate_dim: Optional[int] = None,
        torch_dtype: Optional[Union[str, torch.dtype]] = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        
        # Handle legacy config dicts
        text_config_dict: Optional[Dict] = kwargs.pop('text_config_dict', None)
        vision_config_dict: Optional[Dict] = kwargs.pop('vision_config_dict', None)
        
        self.use_text_flash_attn = use_text_flash_attn
        self.use_vision_xformers = use_vision_xformers
        self.matryoshka_dimensions = matryoshka_dimensions or [32, 64, 128, 256, 512, 768, 1024]
        self.truncate_dim = truncate_dim
        
        # Initialize text config
        if text_config_dict is not None:
            if text_config is None:
                text_config = {}
            text_config.update(text_config_dict)
            
        if text_config is None:
            text_config = {}
            logger.info(
                '`text_config` is `None`. Initializing the `JinaCLIPTextConfig` with '
                'default values.'
            )
            
        # Initialize vision config
        if vision_config_dict is not None:
            if vision_config is None:
                vision_config = {}
            vision_config.update(vision_config_dict)
            
        if vision_config is None:
            vision_config = {}
            logger.info(
                '`vision_config` is `None`. initializing the `JinaCLIPVisionConfig` '
                'with default values.'
            )
            
        self.text_config = JinaCLIPTextConfig(**text_config)
        self.vision_config = JinaCLIPVisionConfig(**vision_config)
        
        self.add_projections = add_projections
        self.projection_dim = projection_dim
        self.logit_scale_init_value = logit_scale_init_value
        self.initializer_factor = 1.0
        
        # Validate embedding dimensions
        if not self.add_projections:
            if self.text_config.embed_dim != self.vision_config.embed_dim:
                raise ValueError(
                    'When projections are disabled (`add_projections=False`), text '
                    'and vision towers need to have the same embedding dimensionality. '
                    f'Currently text embedding dim is {self.text_config.embed_dim} != '
                    f'{self.vision_config.embed_dim} of the vision tower. '
                    'Either set the same output dim for both towers, or enable '
                    'projections with `add_projections=True`.'
                )
                
        # Handle torch dtype
        if (
            torch_dtype
            and hasattr(torch, torch_dtype)
            and type(getattr(torch, torch_dtype)) is torch.dtype
        ):
            self.torch_dtype = getattr(torch, torch_dtype)
        else:
            self.torch_dtype = torch_dtype

    @classmethod
    def from_text_vision_configs(
        cls,
        text_config: JinaCLIPTextConfig,
        vision_config: JinaCLIPVisionConfig,
        **kwargs,
    ):
        return cls(
            text_config=text_config.to_dict(),
            vision_config=vision_config.to_dict(),
            **kwargs,
        )

    def to_dict(self):
        output = deepcopy(self.__dict__)
        output['text_config'] = self.text_config.to_dict()
        output['vision_config'] = self.vision_config.to_dict()
        output['model_type'] = self.__class__.model_type
        return output
