# SPDX-License-Identifier: Apache-2.0

"""Jina CLIP model implementation for vLLM."""

import warnings
from typing import Iterable, List, Optional, Tuple, Union, Dict, Any

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import PretrainedConfig

from vllm.attention import AttentionMetadata
from vllm.config import VllmConfig
from vllm.model_executor.layers.pooler import PoolingType
from vllm.model_executor.layers.quantization import QuantizationConfig
from vllm.model_executor.model_loader import default_weight_loader
from vllm.model_executor.models.adapters import as_embedding_model
from vllm.model_executor.models.interfaces import SupportsMultiModal
from vllm.model_executor.pooling_metadata import PoolingMetadata
from vllm.model_executor.sampling_metadata import SamplingMetadata
from vllm.multimodal import MULTIMODAL_REGISTRY
from vllm.multimodal.base import MultiModalInputs
from vllm.multimodal.processing import BaseMultiModalProcessor
from vllm.sequence import IntermediateTensors, PoolerOutput
from vllm.transformers_utils.configs.jina_clip import (
    JinaCLIPConfig,
    JinaCLIPTextConfig,
    JinaCLIPVisionConfig,
)

from .utils import init_vllm_registered_model


class LayerNorm(nn.LayerNorm):
    """Subclass torch's LayerNorm (with cast back to input dtype)."""

    def forward(self, x: torch.Tensor):
        orig_type = x.dtype
        x = F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        return x.to(orig_type)


class JinaCLIPTextEncoder(nn.Module):
    """Text encoder for Jina CLIP model."""

    def __init__(self, config: JinaCLIPTextConfig, quant_config: Optional[QuantizationConfig] = None):
        super().__init__()
        self.config = config
        self.embed_dim = config.embed_dim
        self.pooler_type = config.pooler_type or "mean_pooler"

        # Note: In a production implementation, this would load the actual
        # Jina-XLM-RoBERTa model from config.hf_model_name_or_path
        # For now, we use a simplified transformer-based implementation

        # Simplified text encoder architecture
        self.vocab_size = 250000  # Approximate vocab size for XLM-RoBERTa
        self.max_position_embeddings = 8192  # Jina supports 8K context

        # Embeddings
        self.word_embeddings = nn.Embedding(self.vocab_size, config.embed_dim)
        self.position_embeddings = nn.Embedding(self.max_position_embeddings, config.embed_dim)
        self.token_type_embeddings = nn.Embedding(2, config.embed_dim)

        # Layer norm and dropout
        self.embeddings_layer_norm = nn.LayerNorm(config.embed_dim)
        self.embeddings_dropout = nn.Dropout(0.1)

        # Simplified transformer layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.embed_dim,
            nhead=16,  # Typical for 1024-dim models
            dim_feedforward=config.embed_dim * 4,
            dropout=0.1,
            batch_first=True,
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=24)

        # Output projection if needed
        if config.proj_type:
            self.projection = nn.Linear(config.embed_dim, config.embed_dim, bias=config.proj_bias)
        else:
            self.projection = nn.Identity()

    def forward(self, input_ids: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len = input_ids.shape

        # Create position ids
        position_ids = torch.arange(seq_len, device=input_ids.device).unsqueeze(0).expand(batch_size, -1)

        # Token type ids (all zeros for single sentence)
        token_type_ids = torch.zeros_like(input_ids)

        # Embeddings
        word_embeds = self.word_embeddings(input_ids)
        position_embeds = self.position_embeddings(position_ids)
        token_type_embeds = self.token_type_embeddings(token_type_ids)

        embeddings = word_embeds + position_embeds + token_type_embeds
        embeddings = self.embeddings_layer_norm(embeddings)
        embeddings = self.embeddings_dropout(embeddings)

        # Create attention mask (non-padding tokens)
        attention_mask = (input_ids != 0).float()

        # Apply transformer
        # Note: PyTorch transformer expects mask where True = ignore
        src_key_padding_mask = (input_ids == 0)
        hidden_states = self.transformer(embeddings, src_key_padding_mask=src_key_padding_mask)

        # Pooling
        if self.pooler_type == "mean_pooler":
            # Mean pooling over non-padding tokens
            masked_hidden = hidden_states * attention_mask.unsqueeze(-1)
            pooled = masked_hidden.sum(dim=1) / attention_mask.sum(dim=1, keepdim=True).clamp(min=1)
        elif self.pooler_type == "cls_pooler":
            # Use first token (CLS)
            pooled = hidden_states[:, 0]
        else:
            # Use last non-padding token
            last_indices = attention_mask.sum(dim=1) - 1
            pooled = hidden_states[torch.arange(batch_size), last_indices]

        return self.projection(pooled)


class JinaCLIPVisionEncoder(nn.Module):
    """Vision encoder for Jina CLIP model (EVA02-L based)."""

    def __init__(self, config: JinaCLIPVisionConfig, quant_config: Optional[QuantizationConfig] = None):
        super().__init__()
        self.config = config

        # EVA02-L vision encoder configuration
        self.embed_dim = config.embed_dim
        self.image_size = config.image_size  # 512x512 for v2
        self.patch_size = config.patch_size  # 14x14
        self.width = config.width  # 1024

        # Calculate number of patches
        self.grid_size = self.image_size // self.patch_size  # 36x36 for 512/14
        self.num_patches = self.grid_size ** 2

        # Patch embedding
        self.patch_embedding = nn.Conv2d(
            3, self.width, kernel_size=self.patch_size, stride=self.patch_size, bias=False
        )

        # Class token
        self.class_embedding = nn.Parameter(torch.randn(self.width) / self.width**0.5)

        # Position embeddings (including class token)
        if config.rope_embeddings:
            # For RoPE, we don't use traditional positional embeddings
            self.positional_embedding = None
        else:
            self.positional_embedding = nn.Parameter(
                torch.randn(self.num_patches + 1, self.width) / self.width**0.5
            )

        # Layer norm
        self.ln_pre = LayerNorm(self.width)

        # Transformer layers (EVA02-L has 24 layers)
        self.layers = nn.ModuleList([
            self._make_layer(config) for _ in range(config.layers)
        ])

        # Final layer norm
        if config.post_norm:
            self.ln_post = LayerNorm(self.width)
        else:
            self.ln_post = nn.Identity()

        # Output projection
        if config.proj_type:
            self.projection = nn.Linear(self.width, config.embed_dim, bias=False)
        else:
            self.projection = nn.Identity()

    def _make_layer(self, config: JinaCLIPVisionConfig) -> nn.Module:
        """Create a transformer layer with EVA02 features."""
        # Simplified implementation - in practice this would include
        # features like SwiGLU, LayerScale, etc.
        return nn.TransformerEncoderLayer(
            d_model=self.width,
            nhead=self.width // config.head_width,
            dim_feedforward=int(self.width * config.mlp_ratio),
            dropout=config.patch_dropout if hasattr(config, 'patch_dropout') else 0.0,
            batch_first=True,
        )

    def forward(self, pixel_values: torch.Tensor) -> torch.Tensor:
        batch_size = pixel_values.shape[0]

        # Patch embedding
        x = self.patch_embedding(pixel_values)  # [batch, width, grid, grid]
        x = x.reshape(batch_size, self.width, -1)  # [batch, width, num_patches]
        x = x.permute(0, 2, 1)  # [batch, num_patches, width]

        # Add class token
        class_token = self.class_embedding.expand(batch_size, 1, -1)
        x = torch.cat([class_token, x], dim=1)  # [batch, num_patches + 1, width]

        # Add positional embeddings (if not using RoPE)
        if self.positional_embedding is not None:
            x = x + self.positional_embedding

        # Pre-norm
        x = self.ln_pre(x)

        # Apply transformer layers
        for layer in self.layers:
            x = layer(x)

        # Use class token for final representation
        x = x[:, 0]  # [batch, width]

        # Post-norm
        x = self.ln_post(x)

        # Final projection
        return self.projection(x)


class JinaCLIPModel(nn.Module, SupportsMultiModal):
    """Jina CLIP model for multimodal embeddings."""

    def __init__(self, *, vllm_config: VllmConfig, prefix: str = ""):
        super().__init__()
        config = vllm_config.model_config.hf_config
        quant_config = vllm_config.quant_config

        if not isinstance(config, JinaCLIPConfig):
            raise ValueError(f"Expected JinaCLIPConfig, got {type(config)}")

        self.config = config
        self.quant_config = quant_config

        # Initialize encoders
        self.text_encoder = JinaCLIPTextEncoder(config.text_config, quant_config)
        self.vision_encoder = JinaCLIPVisionEncoder(config.vision_config, quant_config)

        # Logit scale parameter
        self.logit_scale = nn.Parameter(torch.tensor(config.logit_scale_init_value))

        # Projections if needed
        if config.add_projections:
            self.text_projection = nn.Linear(
                config.text_config.embed_dim, config.projection_dim, bias=False
            )
            self.visual_projection = nn.Linear(
                config.vision_config.embed_dim, config.projection_dim, bias=False
            )
        else:
            self.text_projection = nn.Identity()
            self.visual_projection = nn.Identity()

    def get_text_features(self, input_ids: torch.Tensor) -> torch.Tensor:
        """Get text features from input_ids."""
        text_features = self.text_encoder(input_ids)
        return self.text_projection(text_features)

    def get_image_features(self, pixel_values: torch.Tensor) -> torch.Tensor:
        """Get image features from pixel_values."""
        image_features = self.vision_encoder(pixel_values)
        return self.visual_projection(image_features)

    def _truncate_embeddings(self, embeddings: torch.Tensor, truncate_dim: int) -> torch.Tensor:
        """Truncate embeddings to specified dimension (Matryoshka)."""
        if truncate_dim not in self.config.matryoshka_dimensions:
            warnings.warn(
                f"truncate_dim {truncate_dim} not in matryoshka_dimensions "
                f"{self.config.matryoshka_dimensions}. Truncating anyway."
            )
        return embeddings[:, :truncate_dim]

    def forward(
        self,
        input_ids: Optional[torch.Tensor] = None,
        pixel_values: Optional[torch.Tensor] = None,
        attention_metadata: Optional[AttentionMetadata] = None,
        intermediate_tensors: Optional[IntermediateTensors] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Forward pass for embedding extraction."""
        if input_ids is not None:
            # Text embedding
            return self.get_text_features(input_ids)
        elif pixel_values is not None:
            # Image embedding
            return self.get_image_features(pixel_values)
        else:
            raise ValueError("Either input_ids or pixel_values must be provided")

    def pooler(
        self,
        hidden_states: torch.Tensor,
        pooling_metadata: PoolingMetadata,
    ) -> Optional[PoolerOutput]:
        """Pool the hidden states."""
        # For embedding models, we typically return the hidden states as-is
        # since they're already pooled by the encoders
        return PoolerOutput(outputs=hidden_states)

    def load_weights(self, weights: Iterable[Tuple[str, torch.Tensor]]):
        """Load model weights."""
        params_dict = dict(self.named_parameters())

        for name, loaded_weight in weights:
            if name in params_dict:
                param = params_dict[name]
                default_weight_loader(param, loaded_weight)

    def get_multimodal_embeddings(self, **kwargs) -> Optional[torch.Tensor]:
        """Get multimodal embeddings for the model."""
        # This method is required by SupportsMultiModal interface
        # For embedding models, we typically return None as embeddings
        # are computed in the forward pass
        return None

    def get_input_embeddings(
        self,
        input_ids: torch.Tensor,
        multimodal_embeddings: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Get input embeddings for text."""
        # For embedding models, this typically just returns text embeddings
        return self.get_text_features(input_ids)


class JinaCLIPMultiModalProcessor(BaseMultiModalProcessor):
    """Multimodal processor for Jina CLIP model."""

    def __init__(self, ctx, **kwargs):
        super().__init__(ctx, **kwargs)

    def _get_hf_image_processor(self, model_config, **kwargs):
        # Return a placeholder image processor
        # In practice, this would load the actual Jina CLIP image processor
        return None

    def _get_hf_processor(self, model_config, **kwargs):
        # Return a placeholder processor
        # In practice, this would load the actual Jina CLIP processor
        return None

    def apply(
        self,
        prompt_inputs: MultiModalInputs,
        mm_data: Dict[str, Any],
        hf_processor_mm_kwargs: Dict[str, Any],
    ) -> MultiModalInputs:
        """Apply multimodal processing."""
        # Placeholder implementation
        # In practice, this would process images and text according to Jina CLIP requirements
        return prompt_inputs


# Create embedding version of the model
JinaCLIPEmbeddingModel = as_embedding_model(JinaCLIPModel)
